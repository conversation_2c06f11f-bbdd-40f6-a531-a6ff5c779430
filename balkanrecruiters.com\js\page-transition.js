document.addEventListener('DOMContentLoaded', function() {
    // Hide all x-cloak elements on page load
    document.querySelectorAll('[x-cloak]').forEach(element => {
        element.style.display = 'none';
    });

    // Pre-initialize Alpine.js state to reduce flickering
    document.querySelectorAll('[x-data]').forEach(element => {
        // Mark as initialized to prevent flickering during Alpine initialization
        element.setAttribute('data-alpine-initialized', 'true');
    });
    
    // Add smooth transition when navigating between pages
    document.addEventListener('click', function(e) {
        // Check if the clicked element is a link to another page
        const link = e.target.closest('a');
        if (link && link.href && !link.target && link.href.indexOf(window.location.hostname) > -1) {
            // It's an internal link, apply transition effect
            document.body.style.opacity = '0.8';
            document.body.style.transition = 'opacity 0.15s ease-out';
            
            // Reset opacity after a short delay
            setTimeout(() => {
                document.body.style.opacity = '1';
            }, 50);
        }
    });
});
